# 客户资料收集管理系统 PRD

**版本：** 1.0  
**创建时间：** 2025-08-07  
**产品经理：** [待定]  
**开发团队：** [待定]

---

## 1. 项目概述

### 1.1 产品定位
面向信贷业务的内部客户资料收集管理系统，支持PC端和移动端多场景使用，实现资料收集标准化、流程数字化和团队协同化。

### 1.2 核心价值
- **标准化**：统一资料收集标准，强制执行必填项规则
- **高效化**：多端支持+协同操作，缩短收集周期
- **合规化**：原始文件存储+时间戳记录，保证司法证据效力
- **灵活化**：动态配置资料类目，快速适应业务变化

### 1.3 目标用户
- **主要用户**：信贷业务客服人员（50-100人）
- **管理用户**：系统管理员（2-5人）

---

## 2. 用户角色与权限

| 角色 | 权限范围 | 主要职责 |
|------|---------|---------|
| **客服人员** | 个人订单 + 协同订单 | 创建订单、上传资料、协同操作 |
| **系统管理员** | 全局权限 | 订单管理、类目配置、用户管理 |

---

## 3. 功能需求

### 3.1 客服端功能

#### 3.1.1 订单管理
**创建订单**
- 录入：客户姓名、身份证号、订单编号（自动生成）
- 状态：录入中、已完成

**订单列表**
- 显示：订单编号、客户姓名、创建时间、状态
- 操作：搜索（姓名/订单号）、查看、编辑、删除

**订单操作**
- **暂存**：保存当前进度，无校验限制
- **提交**：必填项校验通过后，状态更新为"已完成"
- **删除**：仅限"录入中"状态的订单

#### 3.1.2 资料上传
**分类管理**
- 按后台配置的资料类目分组展示
- 支持图片（jpg/png/jpeg/heic）、视频（mp4/mov）、文本

**核心特性**
- ✅ **时间戳记录**：自动记录每项资料的录入时间
- ✅ **必填项校验**：红色星号标识 + 提交时强制校验
- ✅ **原始存储**：文件不压缩，保持原始格式
- ✅ **多文件支持**：每个子项可上传多个文件

**移动端特性**
- 📱 **直拍直录**：调用摄像头直接拍照/录像
- 📱 **响应式设计**：适配主流移动设备

#### 3.1.3 协同操作
**分享机制**
- 生成24小时有效期的分享链接和二维码
- 同事扫码后可协同编辑同一订单

**数据同步**
- 多端实时同步
- 操作冲突处理

### 3.2 管理后台功能

#### 3.2.1 仪表盘
- 今日新增订单数
- 本月累计订单数  
- 待处理订单统计

#### 3.2.2 订单管理
**全局视图**
- 展示所有订单，支持多维度筛选
- 筛选条件：订单号、客户姓名、负责客服、时间范围、状态

**详情管理**
- ✅ **时间展示**：显示每项资料的录入时间
- 修改文本信息
- 删除单项资料
- 删除整个订单（二次确认）

#### 3.2.3 类目配置
**资料大类**
- 增删改查（如：身份证明、收入证明、资产证明）
- 调整显示顺序

**资料子项**
- 增删改查（如：身份证正面、身份证反面）
- ✅ **必填项设置**：开关控制是否必填
- 文件类型限制：图片/视频/文本
- 调整显示顺序

#### 3.2.4 用户管理
- 账号创建、编辑、禁用
- 密码重置
- 角色权限分配

---

## 4. 技术要求

### 4.1 技术栈
- **前端**：Vue.js（响应式设计）
- **后端**：node.js
- **数据库**：PostgreSQL
- **文件存储**：本地文件系统，按订单ID分目录

### 4.2 性能要求
- 页面响应时间 ≤ 2秒
- 文件上传：100MB文件在百兆局域网环境下1分钟内完成
- 支持并发用户数：10-50人

### 4.3 兼容性要求
- **PC端**：Chrome、Firefox、Edge最新版
- **移动端**：iOS Safari、Android Chrome

### 4.4 安全要求
- 账号密码认证
- 基于角色的权限控制
- 协同链接时效性控制
- 文件完整性保护

---

## 5. 关键约束

### 5.1 业务约束
- **文件保真**：严禁压缩，保持原始格式
- **时间记录**：所有资料必须记录精确的录入时间
- **必填校验**：后台配置的必填项必须强制校验

### 5.2 技术约束
- 局域网部署，无需考虑公网访问
- 文件存储在服务器本地，无需云存储
- 移动端通过浏览器访问，无需原生APP

---

## 6. 验收标准

### 6.1 功能验收
- [ ] 客服能够完成订单创建到提交的完整流程
- [ ] 管理员能够配置资料类目和必填项
- [ ] 协同操作链接正常工作且具有时效性
- [ ] 必填项校验机制有效运行

### 6.2 性能验收  
- [ ] 页面加载时间符合要求
- [ ] 文件上传速度达标
- [ ] 系统稳定支持预期并发用户数

### 6.3 数据验收
- [ ] 上传文件保持原始格式
- [ ] 时间戳记录准确无误
- [ ] 数据完整性和一致性

---

## 7. 上线计划

### 阶段一：核心功能开发（4-6周）
- 订单管理基础功能
- 资料上传核心功能
- 用户认证和权限控制

### 阶段二：高级功能开发（2-3周）
- 协同操作功能
- 管理后台完整功能
- 移动端优化

### 阶段三：测试和上线（1-2周）
- 功能测试和性能测试
- 用户培训和试运行
- 正式上线和监控

---

**文档状态：** 待评审  
**下一步：** 技术方案设计和开发排期