{"name": "creditsync-backend", "version": "1.0.0", "description": "客户资料收集管理系统 - 后端", "main": "dist/app.js", "scripts": {"dev": "nodemon src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint src --ext .ts --fix", "test": "jest", "migrate": "sequelize-cli db:migrate", "migrate:undo": "sequelize-cli db:migrate:undo", "seed": "sequelize-cli db:seed:all"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "sequelize": "^6.35.2", "pg": "^8.11.3", "redis": "^4.6.12", "winston": "^3.11.0", "node-cron": "^3.0.3", "qrcode": "^1.5.3", "uuid": "^9.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/qrcode": "^1.5.5", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.56.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "sequelize-cli": "^6.6.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}