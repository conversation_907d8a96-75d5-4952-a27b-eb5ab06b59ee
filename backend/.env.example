# 服务器配置
NODE_ENV=development
PORT=3000
FRONTEND_URL=http://localhost:5173

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=materials_db
DB_USER=postgres
DB_PASSWORD=your_password

# Redis 配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT 配置
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=24h

# 文件上传配置
UPLOAD_DIR=uploads
MAX_FILE_SIZE=104857600
ALLOWED_IMAGE_TYPES=jpg,jpeg,png,heic
ALLOWED_VIDEO_TYPES=mp4,mov

# 协同操作配置
COLLABORATION_LINK_EXPIRES=24h

# 日志配置
LOG_LEVEL=info
LOG_FILE=logs/app.log
