# 客户资料收集管理系统 - 技术架构文档

**版本：** 1.0  
**创建时间：** 2025-08-07  
**技术负责人：** [待定]  
**架构师：** [待定]

---

## 1. 整体架构设计

### 1.1 架构原则
- **前后端分离**：Vue.js前端 + Node.js后端API
- **响应式设计**：一套代码适配PC和移动端
- **模块化开发**：功能模块解耦，便于维护扩展
- **数据安全**：文件不压缩，完整性校验

### 1.2 系统架构图
```
┌─────────────────┐    ┌─────────────────┐
│   PC端浏览器     │    │   移动端浏览器   │
│   (Vue.js)      │    │   (Vue.js)      │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │ HTTP/HTTPS
          ┌──────────▼───────────┐
          │     Nginx反向代理     │
          └──────────┬───────────┘
                     │
          ┌──────────▼───────────┐
          │   Node.js后端服务    │
          │   (Express.js)       │
          └─────┬──────────┬─────┘
                │          │
      ┌─────────▼──┐   ┌───▼────────┐
      │PostgreSQL  │   │本地文件存储 │
      │   数据库   │   │(/uploads)  │
      └────────────┘   └────────────┘
```

---

## 2. 前端技术架构

### 2.1 技术栈选择
```javascript
// 核心框架
- Vue.js 3.x (Composition API)
- Vue Router 4.x (路由管理)
- Pinia (状态管理)

// UI框架
- Element Plus (PC端组件库)
- 响应式设计适配移动端

// 构建工具
- Vite (构建工具)
- TypeScript (类型检查)

// 其他工具
- Axios (HTTP客户端)
- Dayjs (日期处理)
- Vue3-dropzone (文件上传)
```

### 2.2 目录结构
```
src/
├── components/          # 公共组件
│   ├── FileUpload/     # 文件上传组件
│   ├── OrderForm/      # 订单表单组件
│   └── Layout/         # 布局组件
├── views/              # 页面组件
│   ├── customer/       # 客服端页面
│   └── admin/          # 管理端页面
├── stores/             # Pinia状态管理
├── utils/              # 工具函数
├── api/                # API接口
└── assets/             # 静态资源
```

### 2.3 响应式设计方案
```css
/* 断点设计 */
$mobile: 768px;
$tablet: 1024px;
$desktop: 1440px;

/* 移动端优化 */
.mobile-upload {
  @media (max-width: $mobile) {
    .camera-btn {
      display: flex; /* 显示拍照按钮 */
    }
  }
}
```

---

## 3. 后端服务架构

### 3.1 技术栈选择
```javascript
// 核心框架
- Node.js 18.x
- Express.js 4.x
- TypeScript

// 数据库
- Sequelize ORM (PostgreSQL)
- Redis (会话缓存)

// 文件处理
- Multer (文件上传)
- Sharp (图片处理，仅用于缩略图)
- FFmpeg (视频缩略图生成)

// 安全认证
- JWT (用户认证)
- bcrypt (密码加密)
- helmet (安全头)

// 其他工具
- Winston (日志记录)
- Joi (参数验证)
- node-cron (定时任务)
```

### 3.2 服务架构
```
src/
├── controllers/        # 控制器层
│   ├── auth.js        # 用户认证
│   ├── orders.js      # 订单管理
│   ├── uploads.js     # 文件上传
│   └── admin.js       # 后台管理
├── models/            # 数据模型
├── services/          # 业务逻辑层
├── middleware/        # 中间件
├── routes/            # 路由定义
├── utils/             # 工具函数
└── config/            # 配置文件
```

### 3.3 核心中间件
```javascript
// 1. 认证中间件
const authMiddleware = (req, res, next) => {
  // JWT token验证
  // 用户权限检查
}

// 2. 文件上传中间件
const uploadMiddleware = multer({
  storage: multer.diskStorage({
    destination: (req, file, cb) => {
      const orderDir = `/uploads/${req.body.orderId}/`
      cb(null, orderDir)
    },
    filename: (req, file, cb) => {
      // 保持原始文件名 + 时间戳
      const timestamp = Date.now()
      cb(null, `${timestamp}_${file.originalname}`)
    }
  }),
  limits: { fileSize: 100 * 1024 * 1024 } // 100MB限制
})

// 3. 时间戳记录中间件
const timestampMiddleware = (req, res, next) => {
  req.uploadTime = new Date().toISOString()
  next()
}
```

---

## 4. 数据库设计

### 4.1 核心表结构
```sql
-- 用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'customer_service',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 订单表
CREATE TABLE orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(100) NOT NULL,
    customer_id_card VARCHAR(18),
    creator_id INTEGER REFERENCES users(id),
    status VARCHAR(20) DEFAULT 'in_progress',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 资料分类表
CREATE TABLE material_categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 资料子项表
CREATE TABLE material_items (
    id SERIAL PRIMARY KEY,
    category_id INTEGER REFERENCES material_categories(id),
    name VARCHAR(100) NOT NULL,
    file_types JSON, -- ['image', 'video', 'text']
    is_required BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 上传文件表
CREATE TABLE uploaded_files (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    material_item_id INTEGER REFERENCES material_items(id),
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(50),
    upload_time TIMESTAMP NOT NULL,
    uploader_id INTEGER REFERENCES users(id),
    text_content TEXT, -- 文本类型资料
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 协同操作表
CREATE TABLE collaboration_links (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES orders(id),
    token VARCHAR(100) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4.2 索引优化
```sql
-- 性能优化索引
CREATE INDEX idx_orders_creator ON orders(creator_id);
CREATE INDEX idx_orders_status ON orders(status);
CREATE INDEX idx_orders_created ON orders(created_at);
CREATE INDEX idx_files_order ON uploaded_files(order_id);
CREATE INDEX idx_files_upload_time ON uploaded_files(upload_time);
CREATE INDEX idx_collaboration_token ON collaboration_links(token);
```

---

## 5. 文件存储方案

### 5.1 存储结构
```
/uploads/
├── orders/
│   ├── [order_id]/
│   │   ├── images/
│   │   │   ├── [timestamp]_[original_name].jpg
│   │   │   └── thumbnails/
│   │   │       └── thumb_[timestamp]_[original_name].jpg
│   │   ├── videos/
│   │   │   ├── [timestamp]_[original_name].mp4
│   │   │   └── thumbnails/
│   │   │       └── thumb_[timestamp]_[original_name].jpg
│   │   └── metadata.json
│   └── [order_id]/
└── temp/                # 临时上传目录
```

### 5.2 文件处理策略
```javascript
// 文件上传处理
const fileProcessor = {
  // 原文件存储（不压缩）
  storeOriginal: async (file, orderDir) => {
    const originalPath = `${orderDir}/original_${file.filename}`
    await fs.copyFile(file.path, originalPath)
    return originalPath
  },
  
  // 生成缩略图（仅用于预览）
  generateThumbnail: async (filePath, type) => {
    if (type === 'image') {
      // 使用Sharp生成图片缩略图
      await sharp(filePath)
        .resize(200, 200)
        .jpeg({ quality: 80 })
        .toFile(`${filePath}_thumb.jpg`)
    } else if (type === 'video') {
      // 使用FFmpeg生成视频封面
      await ffmpeg(filePath)
        .screenshots({
          count: 1,
          timemarks: ['00:00:01'],
          filename: `${filePath}_thumb.jpg`
        })
    }
  },
  
  // 文件完整性校验
  verifyIntegrity: async (filePath) => {
    const stats = await fs.stat(filePath)
    return {
      size: stats.size,
      checksum: await generateChecksum(filePath)
    }
  }
}
```

---

## 6. API接口设计

### 6.1 RESTful API规范
```javascript
// 认证相关
POST   /api/auth/login          # 用户登录
POST   /api/auth/logout         # 用户登出
GET    /api/auth/profile        # 获取用户信息

// 订单管理
GET    /api/orders              # 获取订单列表
POST   /api/orders              # 创建新订单
GET    /api/orders/:id          # 获取订单详情
PUT    /api/orders/:id          # 更新订单
DELETE /api/orders/:id          # 删除订单

// 文件上传
POST   /api/uploads             # 上传文件
GET    /api/uploads/:id         # 获取文件信息
DELETE /api/uploads/:id         # 删除文件

// 协同操作
POST   /api/collaboration       # 生成协同链接
GET    /api/collaboration/:token # 通过token访问订单

// 后台管理
GET    /api/admin/categories    # 获取资料分类
POST   /api/admin/categories    # 创建分类
PUT    /api/admin/categories/:id # 更新分类
DELETE /api/admin/categories/:id # 删除分类
```

### 6.2 响应格式标准
```javascript
// 成功响应
{
  "success": true,
  "data": {...},
  "message": "操作成功",
  "timestamp": "2025-08-07T10:30:00Z"
}

// 错误响应
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "必填项未完成",
    "details": ["客户姓名不能为空", "身份证号格式错误"]
  },
  "timestamp": "2025-08-07T10:30:00Z"
}
```

---

## 7. 部署架构

### 7.1 服务器配置
```yaml
# 推荐配置
CPU: 4核
内存: 8GB
硬盘: 500GB SSD (系统) + 2TB HDD (文件存储)
网络: 千兆网卡
操作系统: Ubuntu 20.04 LTS
```

### 7.2 部署方案
```bash
# 1. 使用PM2管理Node.js进程
npm install -g pm2
pm2 start ecosystem.config.js

# 2. Nginx配置
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/html;
        try_files $uri $uri/ /index.html;
    }
    
    # API代理
    location /api {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
    
    # 文件上传配置
    client_max_body_size 100M;
    proxy_timeout 300s;
}

# 3. PostgreSQL配置优化
max_connections = 100
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 64MB
```

### 7.3 监控和备份
```bash
# 1. 日志监控
pm2 logs --lines 1000

# 2. 数据库备份（每日凌晨2点）
0 2 * * * pg_dump -h localhost -U postgres -d materials_db > /backup/db_$(date +\%Y\%m\%d).sql

# 3. 文件备份（每日凌晨3点）
0 3 * * * rsync -av /uploads/ /backup/files/$(date +\%Y\%m\%d)/
```

---

## 8. 性能优化

### 8.1 前端优化
- **代码分割**：按路由懒加载
- **缓存策略**：静态资源长缓存
- **图片优化**：WebP格式支持
- **移动端优化**：触摸优化、滚动性能

### 8.2 后端优化
- **数据库连接池**：复用数据库连接
- **Redis缓存**：热点数据缓存
- **文件流传输**：大文件分块传输
- **并发控制**：上传队列管理

### 8.3 存储优化
- **文件去重**：相同文件复用存储
- **冷热数据分离**：旧订单迁移到冷存储
- **定期清理**：删除临时文件和过期链接

---

## 9. 安全方案

### 9.1 认证安全
- JWT token过期机制
- 密码复杂度要求
- 登录失败次数限制
- 会话管理和单点登录

### 9.2 文件安全
- 文件类型白名单验证
- 文件大小限制
- 病毒扫描集成
- 文件访问权限控制

### 9.3 数据安全
- 数据库连接加密
- 敏感数据脱敏
- 操作日志记录
- 定期安全审计

---

**文档状态：** 待评审  
**下一步：** 详细开发计划和技术选型确认